using UnityEngine;

namespace Factory.Simulation
{
    public class SelectionManager : MonoBehaviour
    {
        [Header("Dependencies")]
        [SerializeField] private ObjectPlacementManager objectPlacementManager;

        // --- NEW: LayerMask for efficient raycasting ---
        [Header("Settings")]
        [Tooltip("Set this to 'Default' and 'Ground' layers.")]
        [SerializeField] private LayerMask selectionLayerMask;

        private GameObject currentlySelectedObject;
        private RobotController selectedRobotController;
        private Camera mainCamera;
        private Quaternion objectRotation;

        private void Start()
        {
            mainCamera = Camera.main;
        }

        private void Update()
        {
            if (currentlySelectedObject != null && GameManager.Instance.CurrentMode == EGameMode.Selection && (Input.GetKeyDown(KeyCode.Delete) || Input.GetKeyDown(KeyCode.Backspace)))
            {
                DeleteCurrentSelection();
                return;
            }

            if (Input.GetMouseButtonDown(1) && selectedRobotController != null)
            {
                IssueMoveCommand();
            }
            
            switch (GameManager.Instance.CurrentMode)
            {
                case EGameMode.Selection:
                    HandleSelectionInput();
                    break;
                case EGameMode.Translate:
                    HandleTranslationInput();
                    break;
            }
        }

        private void HandleSelectionInput()
        {
            if (Input.GetMouseButtonDown(0))
            {
                // --- MODIFIED: Added layerMask parameter ---
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit, float.MaxValue, selectionLayerMask))
                {
                    Selectable selectable = hit.collider.GetComponentInParent<Selectable>();
                    if (selectable != null)
                    {
                        SelectObject(selectable.gameObject);
                        GameManager.Instance.SetMode(EGameMode.Translate);
                    }
                    else
                    {
                        DeselectObject();
                    }
                }
                else
                {
                    DeselectObject();
                }
            }
        }

        private void HandleTranslationInput()
        {
            if (Input.GetMouseButtonUp(0))
            {
                if (selectedRobotController != null)
                {
                    selectedRobotController.EndManualTranslation();
                }
                GameManager.Instance.SetMode(EGameMode.Selection);
                return;
            }

            // --- MODIFIED: Added layerMask parameter, but only for the 'Ground' layer ---
            // We create a specific mask here for this one action.
            int groundLayerOnly = LayerMask.GetMask("Ground");
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, float.MaxValue, groundLayerOnly))
            {
                Vector3 targetPosition = hit.point;
                if (currentlySelectedObject.GetComponent<RobotController>() == null)
                {
                    targetPosition = GetSnappedPosition(hit.point);
                }
                currentlySelectedObject.transform.position = targetPosition;
            }

            if (Input.GetKeyDown(KeyCode.R))
            {
                float rotationAmount = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift) ? -90f : 90f;
                objectRotation *= Quaternion.Euler(0, rotationAmount, 0);
                currentlySelectedObject.transform.rotation = objectRotation;
            }
        }

        private void IssueMoveCommand()
        {
            // --- MODIFIED: Added layerMask parameter ---
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, float.MaxValue, selectionLayerMask))
            {
                // We still check the tag to be sure we're not moving onto an obstacle.
                if (hit.collider.CompareTag("Ground"))
                {
                    selectedRobotController.MoveTo(hit.point);
                }
            }
        }
        
        // --- No changes to other methods below this line ---
        private void SelectObject(GameObject objectToSelect) { /*...*/ }
        private void DeselectObject() { /*...*/ }
        private void DeleteCurrentSelection() { /*...*/ }
        private Vector3 GetSnappedPosition(Vector3 originalWorldPosition) { /*...*/ }
    }
}